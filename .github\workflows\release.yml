name: 🚀 Simple Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  NODE_VERSION: '18'
  DOCKER_USERNAME: tongnguyen

jobs:
  # ==================== VALIDATE & BUILD RELEASE ====================

  build-release:
    name: 🏗️ Build & Release
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🏷️ Extract version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=${GITHUB_REF#refs/tags/}
          fi

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Release version: $VERSION"

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 🧪 Quick test
        run: |
          # Frontend build test
          cd frontend
          npm ci --legacy-peer-deps
          npm run build

          # Backend test
          cd ../backend
          npm ci
          npm test || echo "No tests configured"

      - name: 🛠️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: 🧱 Build and push release images
        run: |
          VERSION=${{ steps.version.outputs.version }}

          # Build and push backend
          docker build -t ${{ env.DOCKER_USERNAME }}/backend:$VERSION ./backend
          docker build -t ${{ env.DOCKER_USERNAME }}/backend:latest ./backend
          docker push ${{ env.DOCKER_USERNAME }}/backend:$VERSION
          docker push ${{ env.DOCKER_USERNAME }}/backend:latest

          # Build and push frontend
          docker build -t ${{ env.DOCKER_USERNAME }}/frontend:$VERSION ./frontend
          docker build -t ${{ env.DOCKER_USERNAME }}/frontend:latest ./frontend
          docker push ${{ env.DOCKER_USERNAME }}/frontend:$VERSION
          docker push ${{ env.DOCKER_USERNAME }}/frontend:latest

  # ==================== RELEASE NOTIFICATION ====================

  release-notification:
    name: 📢 Release Notification
    runs-on: ubuntu-latest
    needs: [build-release]
    if: always()

    steps:
      - name: 📢 Notify release completion
        run: |
          echo "🎉 Release ${{ needs.build-release.outputs.version || github.ref_name }} completed!"
          echo "Docker Images:"
          echo "  - ${{ env.DOCKER_USERNAME }}/backend:${{ needs.build-release.outputs.version || github.ref_name }}"
          echo "  - ${{ env.DOCKER_USERNAME }}/frontend:${{ needs.build-release.outputs.version || github.ref_name }}"
