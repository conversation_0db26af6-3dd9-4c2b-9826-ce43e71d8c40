services:
  frontend:
    image: tongnguyen/frontend:latest
    container_name: cnpm_frontend
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NODE_ENV=${NODE_ENV}
    expose:
      - "3000"
    depends_on:
      - backend
    networks:
      - cnpm_network
    restart: unless-stopped

  backend:
    image: tongnguyen/backend:latest
    container_name: cnpm_backend
    environment:
      - NODE_ENV=${NODE_ENV}
      - PORT=8080
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DATABASE_URL=mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    expose:
      - "8080"
    depends_on:
      - db-mysql
    networks:
      - cnpm_network
    restart: unless-stopped

  nginx:
    image: nginx:latest
    container_name: cnpm_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - frontend
      - backend
    networks:
      - cnpm_network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    container_name: cnpm_mysql
    environment:
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - cnpm_network
    restart: always

volumes:
  db_data:

networks:
  cnpm_network:
    driver: bridge
