<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752212656095" clover="3.2.0">
  <project timestamp="1752212656096" name="All files">
    <metrics statements="2706" coveredstatements="0" conditionals="1902" coveredconditionals="0" methods="674" coveredmethods="0" elements="5282" coveredelements="0" complexity="0" loc="2706" ncloc="2706" packages="38" files="61" classes="61"/>
    <package name="app">
      <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\layout.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\page.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.(authenticated)">
      <metrics statements="11" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\(authenticated)\layout.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin">
      <metrics statements="7" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\layout.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.appointments">
      <metrics statements="191" coveredstatements="0" conditionals="113" coveredconditionals="0" methods="54" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\appointments\page.tsx">
        <metrics statements="191" coveredstatements="0" conditionals="113" coveredconditionals="0" methods="54" coveredmethods="0"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="431" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="489" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="533" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="626" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="633" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="682" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="683" count="0" type="stmt"/>
        <line num="684" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="687" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="734" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="750" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="771" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="782" count="0" type="stmt"/>
        <line num="798" count="0" type="stmt"/>
        <line num="821" count="0" type="stmt"/>
        <line num="829" count="0" type="stmt"/>
        <line num="830" count="0" type="stmt"/>
        <line num="842" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="853" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.dashboard">
      <metrics statements="45" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\dashboard\page.tsx">
        <metrics statements="45" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.doctors">
      <metrics statements="137" coveredstatements="0" conditionals="89" coveredconditionals="0" methods="44" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\doctors\page.tsx">
        <metrics statements="137" coveredstatements="0" conditionals="89" coveredconditionals="0" methods="44" coveredmethods="0"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.profile">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\profile\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.schedules">
      <metrics statements="114" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="35" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\schedules\page.tsx">
        <metrics statements="114" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="35" coveredmethods="0"/>
        <line num="56" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="454" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.specialties">
      <metrics statements="118" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="34" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\specialties\page.tsx">
        <metrics statements="118" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.users">
      <metrics statements="201" coveredstatements="0" conditionals="135" coveredconditionals="0" methods="52" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\page.tsx">
        <metrics statements="201" coveredstatements="0" conditionals="135" coveredconditionals="0" methods="52" coveredmethods="0"/>
        <line num="69" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="356" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="461" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="665" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="666" count="0" type="stmt"/>
        <line num="667" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="668" count="0" type="stmt"/>
        <line num="669" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="670" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="718" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="747" count="0" type="stmt"/>
        <line num="756" count="0" type="stmt"/>
        <line num="785" count="0" type="stmt"/>
        <line num="812" count="0" type="stmt"/>
        <line num="815" count="0" type="stmt"/>
        <line num="829" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="830" count="0" type="stmt"/>
        <line num="847" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="881" count="0" type="stmt"/>
        <line num="891" count="0" type="stmt"/>
        <line num="899" count="0" type="stmt"/>
        <line num="909" count="0" type="stmt"/>
        <line num="917" count="0" type="stmt"/>
        <line num="926" count="0" type="stmt"/>
        <line num="929" count="0" type="stmt"/>
        <line num="941" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.users.components">
      <metrics statements="43" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="ClientQuill.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\components\ClientQuill.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="QuillEditor.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\components\QuillEditor.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
      <file name="RichTextEditor.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\components\RichTextEditor.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
      <file name="SimpleEditor.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\components\SimpleEditor.tsx">
        <metrics statements="28" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.admin.users.new">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\admin\users\new\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.api.upload">
      <metrics statements="19" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="route.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\api\upload\route.ts">
        <metrics statements="19" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.auth.login">
      <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\auth\login\page.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.auth.register">
      <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\auth\register\page.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.book_appointment">
      <metrics statements="51" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="BookAppointmentPageInner.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\book_appointment\BookAppointmentPageInner.tsx">
        <metrics statements="50" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\book_appointment\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor">
      <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\layout.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.appointments">
      <metrics statements="150" coveredstatements="0" conditionals="101" coveredconditionals="0" methods="39" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\appointments\page.tsx">
        <metrics statements="150" coveredstatements="0" conditionals="101" coveredconditionals="0" methods="39" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="119" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="539" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="540" count="0" type="stmt"/>
        <line num="541" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="544" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.dashboard">
      <metrics statements="55" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\dashboard\page.tsx">
        <metrics statements="55" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="143" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.medical-records">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\medical-records\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.patients">
      <metrics statements="96" coveredstatements="0" conditionals="100" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\patients\page.tsx">
        <metrics statements="96" coveredstatements="0" conditionals="100" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.profile">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\profile\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.doctor.schedule">
      <metrics statements="26" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\doctor\schedule\page.tsx">
        <metrics statements="26" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient">
      <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\layout.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.appointments">
      <metrics statements="145" coveredstatements="0" conditionals="128" coveredconditionals="0" methods="34" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\appointments\page.tsx">
        <metrics statements="145" coveredstatements="0" conditionals="128" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="478" count="0" type="stmt"/>
        <line num="479" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="534" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="535" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="536" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.appointments.new">
      <metrics statements="41" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\appointments\new\page.tsx">
        <metrics statements="41" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.book_appointment">
      <metrics statements="50" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\book_appointment\page.tsx">
        <metrics statements="50" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.dashboard">
      <metrics statements="46" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\dashboard\page.tsx">
        <metrics statements="46" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.doctors">
      <metrics statements="92" coveredstatements="0" conditionals="83" coveredconditionals="0" methods="23" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\doctors\page.tsx">
        <metrics statements="92" coveredstatements="0" conditionals="83" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.doctors.[id]">
      <metrics statements="80" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="layout.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\doctors\[id]\layout.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\doctors\[id]\page.tsx">
        <metrics statements="79" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.profile">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\profile\page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.schedule">
      <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\schedule\page.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.specialties">
      <metrics statements="75" coveredstatements="0" conditionals="57" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\specialties\page.tsx">
        <metrics statements="75" coveredstatements="0" conditionals="57" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.patient.specialties.[id]">
      <metrics statements="94" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="19" coveredmethods="0"/>
      <file name="SpecialtyDetailClient.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\specialties\[id]\SpecialtyDetailClient.tsx">
        <metrics statements="47" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
      </file>
      <file name="SpecialtyDetailFallback.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\specialties\[id]\SpecialtyDetailFallback.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="page-client.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\specialties\[id]\page-client.tsx">
        <metrics statements="40" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\app\patient\specialties\[id]\page.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="19" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components">
      <metrics statements="158" coveredstatements="0" conditionals="177" coveredconditionals="0" methods="50" coveredmethods="0"/>
      <file name="ErrorBoundary.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ErrorBoundary.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
      <file name="Header.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\Header.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="Navbar.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\Navbar.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
      </file>
      <file name="Profile.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\Profile.tsx">
        <metrics statements="115" coveredstatements="0" conditionals="132" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="205" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="475" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="848" count="0" type="stmt"/>
        <line num="856" count="0" type="stmt"/>
      </file>
      <file name="Sidebar.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\Sidebar.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="61" coveredstatements="0" conditionals="115" coveredconditionals="0" methods="28" coveredmethods="0"/>
      <file name="BackButton.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\BackButton.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="badge.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\badge.tsx">
        <metrics statements="10" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
      </file>
      <file name="button.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\button.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
      </file>
      <file name="card.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\card.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="input.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\input.tsx">
        <metrics statements="16" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
      </file>
      <file name="loading.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\loading.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
      </file>
      <file name="modal.tsx" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\components\ui\modal.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="useAuth.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\hooks\useAuth.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="463" coveredstatements="0" conditionals="270" coveredconditionals="0" methods="76" coveredmethods="0"/>
      <file name="api.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\lib\api.ts">
        <metrics statements="404" coveredstatements="0" conditionals="246" coveredconditionals="0" methods="52" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="371" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="424" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="433" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="451" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="457" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="473" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="490" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="505" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="520" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="541" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="545" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="554" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="563" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="564" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="575" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="581" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="582" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="584" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="596" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="599" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="603" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="622" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="632" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="637" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="640" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="643" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="653" count="0" type="stmt"/>
        <line num="659" count="0" type="stmt"/>
        <line num="661" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="662" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="669" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="670" count="0" type="stmt"/>
        <line num="676" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="677" count="0" type="stmt"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="stmt"/>
        <line num="682" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="684" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="691" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="693" count="0" type="stmt"/>
        <line num="694" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="698" count="0" type="stmt"/>
        <line num="699" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="701" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="702" count="0" type="stmt"/>
        <line num="706" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="713" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="714" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="716" count="0" type="stmt"/>
        <line num="720" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="731" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="735" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="745" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="750" count="0" type="stmt"/>
        <line num="751" count="0" type="stmt"/>
        <line num="752" count="0" type="stmt"/>
        <line num="753" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="756" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="760" count="0" type="stmt"/>
        <line num="761" count="0" type="stmt"/>
        <line num="762" count="0" type="stmt"/>
        <line num="763" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="764" count="0" type="stmt"/>
        <line num="767" count="0" type="stmt"/>
        <line num="772" count="0" type="stmt"/>
        <line num="775" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="779" count="0" type="stmt"/>
        <line num="780" count="0" type="stmt"/>
        <line num="781" count="0" type="stmt"/>
        <line num="782" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="783" count="0" type="stmt"/>
        <line num="786" count="0" type="stmt"/>
        <line num="791" count="0" type="stmt"/>
        <line num="794" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="798" count="0" type="stmt"/>
        <line num="799" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="801" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="802" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="812" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="832" count="0" type="stmt"/>
        <line num="833" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="835" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="836" count="0" type="stmt"/>
        <line num="840" count="0" type="stmt"/>
        <line num="843" count="0" type="stmt"/>
        <line num="845" count="0" type="stmt"/>
        <line num="846" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="847" count="0" type="stmt"/>
        <line num="848" count="0" type="stmt"/>
        <line num="850" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="851" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="853" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="854" count="0" type="stmt"/>
        <line num="857" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="858" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="861" count="0" type="stmt"/>
        <line num="879" count="0" type="stmt"/>
        <line num="882" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="886" count="0" type="stmt"/>
        <line num="887" count="0" type="stmt"/>
        <line num="888" count="0" type="stmt"/>
        <line num="889" count="0" type="stmt"/>
        <line num="891" count="0" type="stmt"/>
        <line num="892" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="897" count="0" type="stmt"/>
        <line num="898" count="0" type="stmt"/>
        <line num="899" count="0" type="stmt"/>
        <line num="900" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="901" count="0" type="stmt"/>
        <line num="904" count="0" type="stmt"/>
        <line num="905" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="906" count="0" type="stmt"/>
        <line num="908" count="0" type="stmt"/>
        <line num="909" count="0" type="stmt"/>
        <line num="911" count="0" type="stmt"/>
        <line num="916" count="0" type="stmt"/>
        <line num="919" count="0" type="stmt"/>
        <line num="920" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="921" count="0" type="stmt"/>
        <line num="923" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="927" count="0" type="stmt"/>
        <line num="928" count="0" type="stmt"/>
        <line num="929" count="0" type="stmt"/>
        <line num="930" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="931" count="0" type="stmt"/>
        <line num="934" count="0" type="stmt"/>
        <line num="935" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="936" count="0" type="stmt"/>
        <line num="938" count="0" type="stmt"/>
        <line num="939" count="0" type="stmt"/>
        <line num="941" count="0" type="stmt"/>
        <line num="946" count="0" type="stmt"/>
        <line num="948" count="0" type="stmt"/>
        <line num="949" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="950" count="0" type="stmt"/>
        <line num="952" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="956" count="0" type="stmt"/>
        <line num="957" count="0" type="stmt"/>
        <line num="958" count="0" type="stmt"/>
        <line num="959" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="960" count="0" type="stmt"/>
        <line num="963" count="0" type="stmt"/>
        <line num="964" count="0" type="stmt"/>
        <line num="967" count="0" type="stmt"/>
        <line num="973" count="0" type="stmt"/>
        <line num="975" count="0" type="stmt"/>
        <line num="976" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="977" count="0" type="stmt"/>
        <line num="982" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="983" count="0" type="stmt"/>
        <line num="985" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="987" count="0" type="stmt"/>
        <line num="991" count="0" type="stmt"/>
        <line num="992" count="0" type="stmt"/>
        <line num="993" count="0" type="stmt"/>
        <line num="994" count="0" type="stmt"/>
        <line num="999" count="0" type="stmt"/>
        <line num="1002" count="0" type="stmt"/>
        <line num="1003" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1008" count="0" type="stmt"/>
        <line num="1009" count="0" type="stmt"/>
        <line num="1010" count="0" type="stmt"/>
        <line num="1011" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1012" count="0" type="stmt"/>
        <line num="1016" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="1017" count="0" type="stmt"/>
        <line num="1021" count="0" type="stmt"/>
        <line num="1029" count="0" type="stmt"/>
        <line num="1031" count="0" type="stmt"/>
        <line num="1036" count="0" type="stmt"/>
        <line num="1038" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1042" count="0" type="stmt"/>
        <line num="1043" count="0" type="stmt"/>
        <line num="1044" count="0" type="stmt"/>
        <line num="1045" count="0" type="stmt"/>
        <line num="1050" count="0" type="stmt"/>
        <line num="1052" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1056" count="0" type="stmt"/>
        <line num="1057" count="0" type="stmt"/>
        <line num="1058" count="0" type="stmt"/>
        <line num="1059" count="0" type="stmt"/>
        <line num="1064" count="0" type="stmt"/>
        <line num="1066" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1071" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1073" count="0" type="stmt"/>
        <line num="1074" count="0" type="stmt"/>
        <line num="1080" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1083" count="0" type="stmt"/>
        <line num="1084" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1088" count="0" type="stmt"/>
        <line num="1089" count="0" type="stmt"/>
        <line num="1090" count="0" type="stmt"/>
        <line num="1091" count="0" type="stmt"/>
        <line num="1097" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1104" count="0" type="stmt"/>
        <line num="1105" count="0" type="stmt"/>
        <line num="1106" count="0" type="stmt"/>
        <line num="1107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1108" count="0" type="stmt"/>
        <line num="1116" count="0" type="stmt"/>
        <line num="1118" count="0" type="stmt"/>
        <line num="1123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1126" count="0" type="stmt"/>
        <line num="1127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1132" count="0" type="stmt"/>
        <line num="1133" count="0" type="stmt"/>
        <line num="1134" count="0" type="stmt"/>
        <line num="1135" count="0" type="stmt"/>
        <line num="1141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1145" count="0" type="stmt"/>
        <line num="1147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1150" count="0" type="stmt"/>
        <line num="1155" count="0" type="stmt"/>
        <line num="1156" count="0" type="stmt"/>
        <line num="1157" count="0" type="stmt"/>
        <line num="1158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1159" count="0" type="stmt"/>
        <line num="1162" count="0" type="stmt"/>
        <line num="1168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1175" count="0" type="stmt"/>
        <line num="1179" count="0" type="stmt"/>
        <line num="1180" count="0" type="stmt"/>
        <line num="1181" count="0" type="stmt"/>
        <line num="1182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1183" count="0" type="stmt"/>
        <line num="1187" count="0" type="stmt"/>
        <line num="1190" count="0" type="stmt"/>
        <line num="1193" count="0" type="stmt"/>
        <line num="1195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1198" count="0" type="stmt"/>
        <line num="1218" count="0" type="stmt"/>
        <line num="1219" count="0" type="stmt"/>
        <line num="1220" count="0" type="stmt"/>
        <line num="1221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1222" count="0" type="stmt"/>
        <line num="1226" count="0" type="stmt"/>
        <line num="1234" count="0" type="stmt"/>
        <line num="1235" count="0" type="stmt"/>
        <line num="1236" count="0" type="stmt"/>
        <line num="1237" count="0" type="stmt"/>
        <line num="1240" count="0" type="stmt"/>
        <line num="1241" count="0" type="stmt"/>
        <line num="1244" count="0" type="stmt"/>
        <line num="1245" count="0" type="stmt"/>
        <line num="1247" count="0" type="stmt"/>
        <line num="1262" count="0" type="stmt"/>
        <line num="1263" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="config.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\lib\config.ts">
        <metrics statements="9" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
      <file name="test-api.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\lib\test-api.ts">
        <metrics statements="19" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="utils.ts" path="F:\CNPM\CNPM_WebSiteDKKhamBenh\frontend\src\lib\utils.ts">
        <metrics statements="31" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
