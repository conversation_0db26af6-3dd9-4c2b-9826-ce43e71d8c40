name: cnpm-websitedkkhambenh
region: sgp1

services:
  # Backend Service
  - name: backend
    source_dir: /backend
    github:
      repo: your-username/CNPM_WebSiteDKKhamBenh
      branch: main
    dockerfile_path: backend/Dockerfile
    http_port: 8080
    instance_count: 1
    instance_size_slug: basic-xxs
    routes:
      - path: /api
    envs:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "8080"
      - key: DB_HOST
        value: ${db.HOSTNAME}
      - key: DB_PORT
        value: ${db.PORT}
      - key: DB_NAME
        value: ${db.DATABASE}
      - key: DB_USER
        value: ${db.USERNAME}
      - key: DB_PASSWORD
        value: ${db.PASSWORD}
      - key: DATABASE_URL
        value: ${db.DATABASE_URL}
      - key: ALLOWED_ORIGINS
        value: ${APP_DOMAIN}

  # Frontend Service
  - name: frontend
    source_dir: /frontend
    github:
      repo: your-username/CNPM_WebSiteDKKhamBenh
      branch: main
    dockerfile_path: frontend/Dockerfile
    http_port: 3000
    instance_count: 1
    instance_size_slug: basic-xxs
    routes:
      - path: /
    envs:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_API_URL
        value: ${APP_DOMAIN}/api/
      - key: API_URL
        value: http://backend:8080/api/

databases:
  - name: db
    engine: MYSQL
    version: "8"
    size: db-s-1vcpu-1gb
    num_nodes: 1
