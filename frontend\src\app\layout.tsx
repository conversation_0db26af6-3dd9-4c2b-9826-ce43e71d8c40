import './globals.css';

export const metadata = {
    title: 'Phòng Khám - <PERSON><PERSON> thống đặt lịch khám bệnh',
    description: '<PERSON><PERSON> thống đặt lịch khám bệnh trực tuyến hiện đại và tiện lợi',
    keywords: '<PERSON>h<PERSON><PERSON> kh<PERSON>m, đặt lịch kh<PERSON>m, b<PERSON><PERSON>, chuy<PERSON><PERSON> khoa, sức khỏe',
    authors: [{ name: 'Phòng Khám Team' }],
};

export const viewport = {
    width: 'device-width',
    initialScale: 1,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="vi" className="scroll-smooth">
            <head>
                <meta charSet="utf-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <link rel="icon" href="/favicon.ico" />
            </head>
            <body className="min-h-screen bg-neutral-50 font-sans antialiased">
                <div id="root">
                    {children}
                </div>
                <div id="modal-root"></div>
                <div id="toast-root"></div>
            </body>
        </html>
    );
}