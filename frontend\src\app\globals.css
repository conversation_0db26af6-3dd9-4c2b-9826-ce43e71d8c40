@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
    --background: 248 250 252;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 37 99 235;
    --primary-foreground: 248 250 252;
    --secondary: 241 245 249;
    --secondary-foreground: 15 23 42;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --accent: 241 245 249;
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68;
    --destructive-foreground: 248 250 252;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 37 99 235;
    --radius: 0.5rem;
}

.dark {
    --background: 2 6 23;
    --foreground: 248 250 252;
    --card: 15 23 42;
    --card-foreground: 248 250 252;
    --popover: 15 23 42;
    --popover-foreground: 248 250 252;
    --primary: 59 130 246;
    --primary-foreground: 15 23 42;
    --secondary: 30 41 59;
    --secondary-foreground: 248 250 252;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 30 41 59;
    --accent-foreground: 248 250 252;
    --destructive: 220 38 38;
    --destructive-foreground: 248 250 252;
    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 59 130 246;
}

@layer base {
    * {
        @apply border-neutral-200;
    }

    body {
        @apply bg-neutral-50 text-neutral-900 font-sans antialiased;
        font-feature-settings: "rlig" 1, "calt" 1;
    }

    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Focus styles */
    *:focus-visible {
        @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
    }

    /* Selection styles */
    ::selection {
        @apply bg-primary-100 text-primary-900;
    }
}

@layer components {

    /* Container utilities */
    .container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .container-sm {
        @apply max-w-3xl mx-auto px-4 sm:px-6;
    }

    .container-xs {
        @apply max-w-md mx-auto px-4;
    }

    /* Navigation utilities */
    .nav-responsive {
        @apply flex items-center space-x-1 overflow-x-auto scrollbar-hide;
    }

    .nav-item-responsive {
        @apply whitespace-nowrap flex-shrink-0;
    }

    /* Card components */
    .card {
        @apply bg-white rounded-xl shadow-soft border border-neutral-200 overflow-hidden;
    }

    .card-hover {
        @apply card transition-all duration-200 hover:shadow-medium hover:-translate-y-1;
    }

    .card-header {
        @apply px-6 py-4 border-b border-neutral-200 bg-neutral-50;
    }

    .card-body {
        @apply px-6 py-4;
    }

    .card-footer {
        @apply px-6 py-4 border-t border-neutral-200 bg-neutral-50;
    }

    /* Form components */
    .form-container {
        @apply max-w-md mx-auto mt-8 p-8 bg-white rounded-2xl shadow-large border border-neutral-200;
    }

    .form-group {
        @apply space-y-2;
    }

    .form-label {
        @apply block text-sm font-medium text-neutral-700;
    }

    .form-input {
        @apply w-full px-4 py-3 border border-neutral-300 rounded-xl text-neutral-900 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
    }

    .form-input-error {
        @apply form-input border-error-500 focus:ring-error-500;
    }

    .form-textarea {
        @apply form-input resize-none;
    }

    .form-select {
        @apply form-input appearance-none bg-white;
    }

    .form-error {
        @apply text-sm text-error-600 mt-1;
    }

    .form-help {
        @apply text-sm text-neutral-500 mt-1;
    }

    /* Button components */
    .btn {
        @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-soft hover:shadow-medium;
    }

    .btn-secondary {
        @apply btn bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus:ring-neutral-500 border border-neutral-300;
    }

    .btn-success {
        @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
    }

    .btn-warning {
        @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
    }

    .btn-error {
        @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
    }

    .btn-outline {
        @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
    }

    .btn-ghost {
        @apply btn text-neutral-600 hover:bg-neutral-100 focus:ring-neutral-500;
    }

    .btn-sm {
        @apply px-4 py-2 text-xs;
    }

    .btn-lg {
        @apply px-8 py-4 text-base;
    }

    /* Link components */
    .link {
        @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
    }

    .link-muted {
        @apply text-neutral-500 hover:text-neutral-700 transition-colors duration-200;
    }

    /* Badge components */
    .badge {
        @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-error {
        @apply badge bg-error-100 text-error-800;
    }

    .badge-neutral {
        @apply badge bg-neutral-100 text-neutral-800;
    }

    /* Loading components */
    .loading-spinner {
        @apply animate-spin rounded-full border-2 border-neutral-200 border-t-primary-600;
    }

    .loading-dots {
        @apply flex space-x-1;
    }

    .loading-dot {
        @apply w-2 h-2 bg-primary-600 rounded-full animate-bounce;
    }

    /* Utility classes */
    .text-gradient {
        @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
    }

    .bg-gradient-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-700;
    }

    .bg-gradient-secondary {
        @apply bg-gradient-to-r from-secondary-600 to-secondary-700;
    }

    .glass {
        @apply bg-white/80 backdrop-blur-sm border border-white/20;
    }

    .glass-dark {
        @apply bg-neutral-900/80 backdrop-blur-sm border border-neutral-700/20;
    }

    /* Scrollbar utilities */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
}