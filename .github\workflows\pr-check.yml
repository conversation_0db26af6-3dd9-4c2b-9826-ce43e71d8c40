name: 🔍 Simple PR Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '18'

jobs:
  # ==================== FRONTEND PR TESTS ====================

  frontend-pr-test:
    name: 🧪 Frontend PR Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🏗️ Build check
        run: npm run build

  # ==================== BACKEND PR TESTS ====================

  backend-pr-test:
    name: 🧪 Backend PR Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm test || echo "No tests configured"
        env:
          DB_HOST: localhost
          DB_PORT: 3306
          DB_NAME: test_db
          DB_USERNAME: root
          DB_PASSWORD: root

  # ==================== PR SUMMARY ====================

  pr-summary:
    name: 📋 PR Summary
    runs-on: ubuntu-latest
    needs: [frontend-pr-test, backend-pr-test]
    if: always()

    steps:
      - name: 📊 Check PR results
        run: |
          echo "Frontend Tests: ${{ needs.frontend-pr-test.result }}"
          echo "Backend Tests: ${{ needs.backend-pr-test.result }}"

          if [[ "${{ needs.frontend-pr-test.result }}" == "failure" ||
                "${{ needs.backend-pr-test.result }}" == "failure" ]]; then
            echo "❌ Some PR tests failed!"
            exit 1
          else
            echo "✅ All PR tests passed!"
          fi
