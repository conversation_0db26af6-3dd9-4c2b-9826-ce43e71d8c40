services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8080/api/}
      - API_URL=http://backend:8080/api/
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=8080
      - DB_HOST=${DB_HOST:-db-mysql}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME:-DBDKKHAMBENH}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-123456}
      - DATABASE_URL=mysql://${DB_USER:-root}:${DB_PASSWORD:-123456}@${DB_HOST:-db-mysql}:${DB_PORT:-3306}/${DB_NAME:-DBDKKHAMBENH}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:3000,http://frontend:3000}
    depends_on:
      - db-mysql
    networks:
      - app-network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_DATABASE=${DB_NAME:-DBDKKHAMBENH}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-123456}
    ports:
      - "${DB_EXTERNAL_PORT:-3307}:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge