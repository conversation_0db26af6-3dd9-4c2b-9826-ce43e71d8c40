{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --watchAll=false", "test:ci:coverage": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathPattern=__tests__/unit", "test:e2e": "playwright test", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@types/uuid": "^10.0.0", "@uiw/react-md-editor": "^4.0.6", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "framer-motion": "^12.7.4", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.479.0", "next": "^15.3.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "babel-jest": "^29.7.0", "@playwright/test": "^1.40.1", "msw": "^2.0.11"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "transform": {"^.+\\.(js|jsx|ts|tsx)$": ["babel-jest", {"presets": ["next/babel"]}]}, "testPathIgnorePatterns": ["<rootDir>/.next/", "<rootDir>/node_modules/", "<rootDir>/e2e/", "<rootDir>/__tests__/integration/"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/pages/_app.tsx", "!src/pages/_document.tsx"], "coverageThreshold": {"global": {"branches": 0, "functions": 0, "lines": 0, "statements": 0}}}}