import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        const file = formData.get('image') as File;

        if (!file) {
            return NextResponse.json(
                { message: 'No file uploaded' },
                { status: 400 }
            );
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            return NextResponse.json(
                { message: 'File must be an image' },
                { status: 400 }
            );
        }

        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            return NextResponse.json(
                { message: 'File size must be less than 5MB' },
                { status: 400 }
            );
        }

        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);

        // Create unique filename
        const uniqueFilename = `${uuidv4()}-${file.name}`;
        const uploadDir = join(process.cwd(), 'public', 'uploads');
        const filePath = join(uploadDir, uniqueFilename);

        // Save file
        await writeFile(filePath, buffer);

        // Return the URL path to the uploaded file
        const imageUrl = `/uploads/${uniqueFilename}`;

        return NextResponse.json({ imageUrl });
    } catch (error) {
        console.error('Error uploading file:', error);
        return NextResponse.json(
            { message: 'Error uploading file' },
            { status: 500 }
        );
    }
} 