# Production Environment Configuration
NODE_ENV=production

# Port Configuration
FRONTEND_PORT=80
BACKEND_PORT=8080
DB_EXTERNAL_PORT=3306

# Server Configuration
SERVER_IP=your-gcp-static-ip

# Database Configuration
DB_HOST=db-mysql
DB_PORT=3306
DB_NAME=DBDKKHAMBENH
DB_USER=root
DB_PASSWORD=your-secure-password

# JWT Configuration (Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")
JWT_SECRET=your-jwt-secret-key

# API URLs
NEXT_PUBLIC_API_URL=http://your-server-ip/api/
ALLOWED_ORIGINS=http://your-server-ip
